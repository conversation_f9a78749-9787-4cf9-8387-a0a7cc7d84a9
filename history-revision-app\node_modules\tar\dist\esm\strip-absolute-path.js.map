{"version": 3, "file": "strip-absolute-path.js", "sourceRoot": "", "sources": ["../../src/strip-absolute-path.ts"], "names": [], "mappings": "AAAA,0EAA0E;AAC1E,OAAO,EAAE,KAAK,EAAE,MAAM,WAAW,CAAA;AACjC,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,KAAK,CAAA;AAEnC,2BAA2B;AAC3B,4EAA4E;AAC5E,yEAAyE;AACzE,0CAA0C;AAC1C,4EAA4E;AAC5E,uEAAuE;AACvE,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,IAAY,EAAE,EAAE;IAChD,IAAI,CAAC,GAAG,EAAE,CAAA;IAEV,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAA;IACxB,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QACvC,yDAAyD;QACzD,2CAA2C;QAC3C,MAAM,IAAI,GACR,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;YACrD,GAAG;YACL,CAAC,CAAC,MAAM,CAAC,IAAI,CAAA;QACf,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC9B,CAAC,IAAI,IAAI,CAAA;QACT,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAA;IACtB,CAAC;IACD,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AAClB,CAAC,CAAA", "sourcesContent": ["// unix absolute paths are also absolute on win32, so we use this for both\nimport { win32 } from 'node:path'\nconst { isAbsolute, parse } = win32\n\n// returns [root, stripped]\n// Note that windows will think that //x/y/z/a has a \"root\" of //x/y, and in\n// those cases, we want to sanitize it to x/y/z/a, not z/a, so we strip /\n// explicitly if it's the first character.\n// drive-specific relative paths on Windows get their root stripped off even\n// though they are not absolute, so `c:../foo` becomes ['c:', '../foo']\nexport const stripAbsolutePath = (path: string) => {\n  let r = ''\n\n  let parsed = parse(path)\n  while (isAbsolute(path) || parsed.root) {\n    // windows will think that //x/y/z has a \"root\" of //x/y/\n    // but strip the //?/C:/ off of //?/C:/path\n    const root =\n      path.charAt(0) === '/' && path.slice(0, 4) !== '//?/' ?\n        '/'\n      : parsed.root\n    path = path.slice(root.length)\n    r += root\n    parsed = parse(path)\n  }\n  return [r, path]\n}\n"]}
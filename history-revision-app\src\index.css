@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-dark-bg text-dark-text font-sans;
    margin: 0;
    min-height: 100vh;
  }

  html {
    @apply dark;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-dark-card hover:bg-slate-600 text-dark-text border border-dark-border font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-dark-card border border-dark-border rounded-lg p-6 shadow-lg;
  }

  .input {
    @apply bg-dark-card border border-dark-border text-dark-text rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
}
